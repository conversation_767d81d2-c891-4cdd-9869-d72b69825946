import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";
// Temporarily simplified - will be replaced with actual Code2Tutor implementation
// import { executeCode2TutorFlow, createDefaultSharedStore } from "@/Agents/Code2Tutor";
// import { SharedStore } from "@/Agents/Code2Tutor/types";
// import {
//   tutorEvents,
//   TutorEventType,
//   ProgressEvent,
//   AgentStatusEvent,
// } from "@/Agents/Code2Tutor/utils/events";
import { CheckCircle, Clock, AlertCircle, Loader2, BookOpen, Target, Zap, Users } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";



interface LogEntry {
  timestamp: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
}

interface TutorialStage {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "error";
  description: string;
  completedIn: string;
  details: string[];
  concepts?: number;
  exercises?: number;
}

interface AgentStatus {
  currentAgent: string;
  agentProgress: number;
  statusMessage: string;
}

const TutorCreationStatus = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  const params = location.state;

  const [progress, setProgress] = useState(0);
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [agentStatus, setAgentStatus] = useState<AgentStatus>({
    currentAgent: "",
    agentProgress: 0,
    statusMessage: "Initializing...",
  });

  const logContainerRef = useRef<HTMLDivElement>(null);

  const [tutorialStages, setTutorialStages] = useState<TutorialStage[]>([
    {
      id: "repository-analysis",
      name: "Repository Analysis",
      status: "pending",
      description: "Analyzing repository structure and extracting educational content",
      completedIn: "",
      details: [],
    },
    {
      id: "concept-extraction",
      name: "Learning Concept Extraction",
      status: "pending",
      description: "Identifying key learning concepts and their relationships",
      completedIn: "",
      details: [],
      concepts: 0,
    },
    {
      id: "tutorial-planning",
      name: "Tutorial Structure Planning",
      status: "pending",
      description: "Designing optimal learning progression and dependencies",
      completedIn: "",
      details: [],
    },
    {
      id: "content-generation",
      name: "Interactive Content Generation",
      status: "pending",
      description: "Creating educational content, exercises, and examples",
      completedIn: "",
      details: [],
      exercises: 0,
    },
    {
      id: "tutorial-assembly",
      name: "Tutorial Assembly",
      status: "pending",
      description: "Combining all components into final interactive tutorial",
      completedIn: "",
      details: [],
    },
  ]);

  const flowInitialized = useRef(false);

  useEffect(() => {
    if (!user) {
      return;
    }

    if (!flowInitialized.current) {
      const repoUrl = params?.repoUrl || "https://github.com/example/demo-repo";
      handleGenerateTutorial(repoUrl, params);
      flowInitialized.current = true;
    }
  }, [params, user]);

  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logEntries]);

  const getTimestamp = (): string => {
    return new Date().toLocaleTimeString();
  };

  const addLogEntry = (message: string, type: LogEntry["type"] = "info") => {
    setLogEntries((prev) => [
      ...prev,
      {
        timestamp: getTimestamp(),
        message,
        type,
      },
    ]);
  };

  const updateStageStatus = (
    stageId: string,
    status: TutorialStage["status"],
    details?: string[],
    additionalData?: any
  ) => {
    setTutorialStages((prev) =>
      prev.map((stage) => {
        if (stage.id === stageId) {
          const updatedStage = {
            ...stage,
            status,
            ...(details && { details }),
            ...(status === "completed" && { completedIn: getTimestamp() }),
            ...additionalData,
          };
          return updatedStage;
        }
        return stage;
      })
    );
  };

  const handleGenerateTutorial = async (repoUrl: string, options: any) => {
    try {
      addLogEntry("🚀 Starting Code2Tutor workflow...", "info");

      // Simulate the tutorial creation process
      const stages = [
        { id: "repository-analysis", name: "Repository Analysis", duration: 2000 },
        { id: "concept-extraction", name: "Concept Extraction", duration: 3000 },
        { id: "tutorial-planning", name: "Tutorial Planning", duration: 2500 },
        { id: "content-generation", name: "Content Generation", duration: 4000 },
        { id: "tutorial-assembly", name: "Tutorial Assembly", duration: 2000 }
      ];

      let currentProgress = 0;
      const progressIncrement = 100 / stages.length;

      for (let i = 0; i < stages.length; i++) {
        const stage = stages[i];

        // Start stage
        updateStageStatus(stage.id, "in-progress");
        setAgentStatus({
          currentAgent: stage.name,
          agentProgress: 0,
          statusMessage: `Starting ${stage.name.toLowerCase()}...`,
        });
        addLogEntry(`🤖 ${stage.name}: Starting`, "info");

        // Simulate progress within stage
        for (let progress = 0; progress <= 100; progress += 20) {
          await new Promise(resolve => setTimeout(resolve, stage.duration / 5));

          setAgentStatus({
            currentAgent: stage.name,
            agentProgress: progress,
            statusMessage: `Processing ${stage.name.toLowerCase()}... ${progress}%`,
          });

          const overallProgress = currentProgress + (progress / 100) * progressIncrement;
          setProgress(overallProgress);

          if (progress === 60 && stage.id === "concept-extraction") {
            addLogEntry(`💡 Concept extracted: React Components (beginner)`, "success");
            updateStageStatus("concept-extraction", "in-progress", undefined, { concepts: 1 });
          }

          if (progress === 80 && stage.id === "concept-extraction") {
            addLogEntry(`💡 Concept extracted: State Management (intermediate)`, "success");
            updateStageStatus("concept-extraction", "in-progress", undefined, { concepts: 2 });
          }

          if (progress === 40 && stage.id === "content-generation") {
            addLogEntry(`📝 Section generated: Introduction to React`, "success");
            updateStageStatus("content-generation", "in-progress", undefined, { exercises: 2 });
          }

          if (progress === 80 && stage.id === "content-generation") {
            addLogEntry(`📝 Section generated: Building Your First Component`, "success");
            updateStageStatus("content-generation", "in-progress", undefined, { exercises: 5 });
          }
        }

        // Complete stage
        currentProgress += progressIncrement;
        updateStageStatus(stage.id, "completed", [`${stage.name} completed successfully`]);
        addLogEntry(`✅ ${stage.name}: Completed`, "success");
      }

      // Final completion
      setProgress(100);
      addLogEntry(`🎉 Tutorial created successfully!`, "success");

      // Simulate tutorial ID and URL
      const tutorialId = `tutorial_${Date.now()}`;
      const tutorialUrl = `/tutorial/${tutorialId}`;
      addLogEntry(`🔗 Tutorial URL: ${tutorialUrl}`, "info");

      toast({
        title: "Tutorial Created!",
        description: "Your interactive tutorial has been generated successfully.",
      });

      // Navigate to tutorial after a delay
      setTimeout(() => {
        navigate(`/dashboard/tutor-gallery`);
      }, 2000);

    } catch (error) {
      console.error("Error running Code2Tutor workflow:", error);
      addLogEntry(`💥 Workflow error: ${error.message}`, "error");

      toast({
        title: "Workflow Error",
        description: "An unexpected error occurred during tutorial generation.",
        variant: "destructive",
      });
    }
  };

  const getStageIcon = (status: TutorialStage["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "in-progress":
        return <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStageColor = (status: TutorialStage["status"]) => {
    switch (status) {
      case "completed":
        return "border-green-200 bg-green-50";
      case "in-progress":
        return "border-blue-200 bg-blue-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <BookOpen className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900">Creating Interactive Tutorial</h1>
        </div>
        <p className="text-xl text-gray-600">
          Transforming your code into an engaging learning experience with exercises and interactive content.
        </p>
        <Link
          to="/dashboard/create-tutor"
          className="text-blue-600 hover:text-blue-700 cursor-pointer"
        >
          <i className="fa-solid fa-arrow-left mr-2"></i>
          Back to configuration
        </Link>
      </div>

      {/* Overall Progress */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Overall Progress</h2>
          <span className="text-lg font-medium text-gray-700">{progress}%</span>
        </div>
        <Progress value={progress} className="h-3 mb-4" />
        
        {agentStatus.currentAgent && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-3">
              <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
              <div>
                <div className="font-semibold text-blue-900">{agentStatus.currentAgent}</div>
                <div className="text-sm text-blue-700">{agentStatus.statusMessage}</div>
                <div className="w-full bg-blue-200 h-2 mt-2 rounded-full overflow-hidden">
                  <div
                    className="bg-blue-600 h-full rounded-full transition-all duration-300"
                    style={{ width: `${agentStatus.agentProgress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Tutorial Stages */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Tutorial Creation Stages</h2>
          
          <div className="space-y-4">
            {tutorialStages.map((stage, index) => (
              <div
                key={stage.id}
                className={`p-4 rounded-lg border ${getStageColor(stage.status)}`}
              >
                <div className="flex items-start space-x-3">
                  {getStageIcon(stage.status)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">{stage.name}</h3>
                      {stage.completedIn && (
                        <span className="text-xs text-gray-500">
                          Completed at {stage.completedIn}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{stage.description}</p>
                    
                    {/* Stage-specific metrics */}
                    {stage.concepts !== undefined && stage.concepts > 0 && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Target className="h-4 w-4 text-blue-600" />
                        <span className="text-sm text-blue-700">{stage.concepts} concepts identified</span>
                      </div>
                    )}
                    
                    {stage.exercises !== undefined && stage.exercises > 0 && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Zap className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-700">{stage.exercises} exercises created</span>
                      </div>
                    )}

                    {stage.details.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {stage.details.map((detail, idx) => (
                          <div key={idx} className="text-xs text-gray-600">
                            • {detail}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Activity Log */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Activity Log</h2>
          
          <div
            ref={logContainerRef}
            className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm"
          >
            {logEntries.map((entry, index) => (
              <div key={index} className="mb-1">
                <span className="text-gray-500">[{entry.timestamp}]</span>{" "}
                <span
                  className={
                    entry.type === "error"
                      ? "text-red-400"
                      : entry.type === "success"
                      ? "text-green-400"
                      : entry.type === "warning"
                      ? "text-yellow-400"
                      : "text-blue-400"
                  }
                >
                  {entry.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Status Toast */}
      {progress < 100 && agentStatus.currentAgent && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-6 py-3 rounded-md flex items-center shadow-lg">
          <Loader2 className="h-5 w-5 text-white mr-3 animate-spin" />
          <div>
            <div className="font-semibold">{agentStatus.currentAgent}</div>
            <div className="text-sm">{agentStatus.statusMessage}</div>
            <div className="w-full bg-white/20 h-1 mt-1 rounded-full overflow-hidden">
              <div
                className="bg-white h-full rounded-full transition-all duration-300"
                style={{ width: `${agentStatus.agentProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorCreationStatus;
